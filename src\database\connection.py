# src/database/connection.py
import os
import mysql.connector
from mysql.connector import Error
from dotenv import load_dotenv

# Load environment variables from the .env file
load_dotenv()

def get_mysql_connection():
    """Establishes a database connection using credentials from the .env file."""
    return mysql.connector.connect(
        host=os.getenv("MYSQL_HOST"),
        user=os.getenv("MYSQL_USER"),
        password=os.getenv("MYSQL_PASSWORD"),
        database=os.getenv("MYSQL_DATABASE"),
        port=int(os.getenv("MYSQL_PORT", 3306))  
    )

def test_mysql_connection():
    """Tests the database connection and raises an error if it fails."""
    try:
        conn = get_mysql_connection()
        conn.close()
        # If the connection succeeds, this function completes without error
    except Error as e:
        # If it fails, re-raise the error to be caught by the main server script
        raise ConnectionError(f"MySQL connection test failed: {str(e)}") from e
    