# How to Create a New Branch After Cloning the Repository

Follow these steps to create a new branch after you have cloned the repository:

1. **Clone the repository** (if you haven't already):

   ```sh
   git clone https://<EMAIL>/BnTProjects/AIandBI/_git/MCPServer
   cd MCPServer
   ```

2. **Check the current branches:**

   ```sh
   git branch
   ```

3. **Create a new branch:**

   ```sh
   git checkout -b <new-branch-name>
   ```

   - Replace `<new-branch-name>` with your desired branch name.

4. **Push the new branch to the remote repository:**

   ```sh
   git push -u origin <new-branch-name>
   ```

---

**Example:**

```sh
git clone https://github.com/yourusername/yourrepo.git
cd yourrepo
git checkout -b features/new-feature
git push -u origin features/new-feature
```

Now you are working on