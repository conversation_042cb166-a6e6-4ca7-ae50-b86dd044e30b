# MCP Server Deployment Guide

This guide provides step-by-step instructions for deploying the MCP Server using Docker Compose.

## Prerequisites

Before deploying, ensure you have the following installed on your deployment server:

- **Docker** (version 20.10 or higher)
- **Docker Compose** (version 2.0 or higher)
- **Git** (for cloning the repository)

### Verify Prerequisites

```bash
# Check Docker version
docker --version

# Check Docker Compose version
docker compose version

# Check Git version
git --version
```

## Deployment Steps

### 1. Clone the Repository

```bash
git clone https://<EMAIL>/BnTProjects/AIandBI/_git/MCPServer
cd MCPServer
```

### 2. Environment Configuration

#### Create Environment File

Copy the example environment file and configure it:

```bash
cp .env.example .env
```

#### Configure Environment Variables

Edit the `.env` file with your specific configuration:

```bash
# MCP Server Configuration
MCP_ENV=prod                                    # Set to 'prod' for production
MCP_TOKEN=your_secure_mcp_token_here           # Generate a secure token
AUTH_SERVER_URL=https://your-auth-server.com   # Your authorization server URL
RESOURCE_SERVER_URL=https://your-server.com    # This server's public URL

# MySQL Database Configuration
MYSQL_HOST=your_mysql_host                      # MySQL server hostname/IP
MYSQL_PORT=3306                                 # MySQL port (default: 3306)
MYSQL_USER=your_mysql_user                      # MySQL username
MYSQL_PASSWORD=your_secure_mysql_password       # MySQL password
MYSQL_DATABASE=your_database_name               # Database name
```

#### Security Considerations

- **MCP_TOKEN**: Generate a strong, unique token for authentication
- **MYSQL_PASSWORD**: Use a strong password for database access
- **File Permissions**: Ensure `.env` file has restricted permissions:
  ```bash
  chmod 600 .env
  ```

### 3. Database Setup

Ensure your MySQL database is properly configured:

1. **Create Database** (if not exists):
   ```sql
   CREATE DATABASE your_database_name;
   ```

2. **Create User** (if needed):
   ```sql
   CREATE USER 'your_mysql_user'@'%' IDENTIFIED BY 'your_secure_mysql_password';
   GRANT ALL PRIVILEGES ON your_database_name.* TO 'your_mysql_user'@'%';
   FLUSH PRIVILEGES;
   ```

3. **Test Connection**: Verify database connectivity before deployment

### 4. Build and Deploy

#### Development Deployment

For development with live code changes:

```bash
# Build and start the container
docker compose up --build

# Or run in detached mode
docker compose up --build -d
```

#### Production Deployment

For production deployment, consider these modifications:

1. **Update compose.yaml** for production:
   ```yaml
   services:
     mcpserver:
       build: .
       container_name: mcpserver
       ports:
         - "8000:8000"
       # Remove volume mount for production
       # volumes:
       #   - .:/app
       environment:
         - MCP_ENV=prod
       restart: unless-stopped
       healthcheck:
         test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
         interval: 30s
         timeout: 10s
         retries: 3
   ```

2. **Deploy**:
   ```bash
   docker compose up --build -d
   ```

### 5. Verify Deployment

#### Check Container Status

```bash
# View running containers
docker compose ps

# Check container logs
docker compose logs mcpserver

# Follow logs in real-time
docker compose logs -f mcpserver
```

#### Test Server Connectivity

```bash
# Test if server is responding
curl -X GET http://localhost:8000/health

# Or test from another machine
curl -X GET http://your-server-ip:8000/health
```

#### Check Application Logs

```bash
# View application logs
docker compose exec mcpserver cat logs/mcp_server.log

# Or check logs directory on host (if volume mounted)
cat logs/mcp_server.log
```

## Configuration Options

### Environment Variables Reference

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `MCP_ENV` | Environment mode (dev/prod) | Yes | dev |
| `MCP_TOKEN` | Authentication token | Yes (prod) | - |
| `AUTH_SERVER_URL` | Authorization server URL | Yes (prod) | - |
| `RESOURCE_SERVER_URL` | This server's URL | Yes (prod) | - |
| `MYSQL_HOST` | MySQL hostname | Yes | - |
| `MYSQL_PORT` | MySQL port | No | 3306 |
| `MYSQL_USER` | MySQL username | Yes | - |
| `MYSQL_PASSWORD` | MySQL password | Yes | - |
| `MYSQL_DATABASE` | Database name | Yes | - |

### Port Configuration

- **Default Port**: 8000
- **Exposed Port**: 8000 (configurable in compose.yaml)
- **Protocol**: HTTP

## Maintenance Operations

### Update Deployment

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker compose down
docker compose up --build -d
```

### View Logs

```bash
# View recent logs
docker compose logs --tail=100 mcpserver

# Follow logs
docker compose logs -f mcpserver
```

### Restart Service

```bash
# Restart the service
docker compose restart mcpserver

# Or stop and start
docker compose down
docker compose up -d
```

### Backup Logs

```bash
# Create logs backup
docker compose exec mcpserver tar -czf /tmp/logs-backup-$(date +%Y%m%d).tar.gz logs/
docker compose cp mcpserver:/tmp/logs-backup-$(date +%Y%m%d).tar.gz ./
```

## Troubleshooting

### Common Issues

1. **Container Won't Start**
   ```bash
   # Check logs for errors
   docker compose logs mcpserver
   
   # Check if port is already in use
   netstat -tulpn | grep :8000
   ```

2. **Database Connection Issues**
   ```bash
   # Test database connectivity from container
   docker compose exec mcpserver python -c "from database.connection import test_mysql_connection; test_mysql_connection()"
   ```

3. **Permission Issues**
   ```bash
   # Check file permissions
   ls -la .env
   
   # Fix permissions if needed
   chmod 600 .env
   ```

4. **Environment Variable Issues**
   ```bash
   # Check if environment variables are loaded
   docker compose exec mcpserver env | grep MCP
   ```

### Health Checks

The server includes comprehensive logging. Monitor these locations:

- **Container logs**: `docker compose logs mcpserver`
- **Application logs**: `logs/mcp_server.log` (if volume mounted)
- **System logs**: Check Docker daemon logs

### Performance Monitoring

```bash
# Monitor container resource usage
docker stats mcpserver

# Check container processes
docker compose exec mcpserver ps aux
```

## Security Best Practices

1. **Environment Variables**: Never commit `.env` files to version control
2. **Network Security**: Use reverse proxy (nginx/traefik) for HTTPS in production
3. **Database Security**: Use dedicated database user with minimal privileges
4. **Token Security**: Rotate MCP tokens regularly
5. **Updates**: Keep Docker images and dependencies updated

## Production Considerations

1. **Reverse Proxy**: Use nginx or traefik for SSL termination
2. **Monitoring**: Implement logging aggregation and monitoring
3. **Backup**: Regular database and configuration backups
4. **Scaling**: Consider container orchestration for high availability
5. **Secrets Management**: Use Docker secrets or external secret management

## Support

For deployment issues:

1. Check the logs: `docker compose logs mcpserver`
2. Verify environment configuration
3. Test database connectivity
4. Review the troubleshooting section above
5. Contact the development team with specific error messages
