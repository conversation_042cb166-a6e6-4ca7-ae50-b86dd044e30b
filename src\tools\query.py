# src/tools/query.py
import sys
import os
from utils import setup_logger
from typing import List, Dict
from mysql.connector import Error
import time
from database.connection import get_mysql_connection

# Set up logging
logger = setup_logger(__name__)

def create_query(mcp):
    """Register the query tool with the MCP server."""

    @mcp.tool()
    def execute_query(query: str, limit: int = 1000, explain: bool = False) -> List[Dict]:
        """Execute a secure, read-only SQL query with support for advanced operations.
        
        Supported operations:
          - SELECT with aggregations (SUM, COUNT, AVG, MIN, MAX)
          - GROUP BY, HAVING
          - JOINs (INNER, LEFT, RIGHT, FULL)
          - Subqueries and CTEs (WITH)
          - ORDER BY, LIMIT
          - Complex WHERE conditions
          - Date/time functions
        
        Args:
            query (str): The SQL query to execute (must start with SELECT, WITH, or EXPLAIN).
            limit (int): Maximum number of rows to return (default: 1000).
            explain (bool): If True, return the query execution plan instead of data.
        
        Returns:
            List of dictionaries containing query results, execution plan, or error message.
        """
        query_upper = query.strip().upper()

        # Allow only SELECT, WITH (CTE), and EXPLAIN queries
        allowed_starters = ["SELECT", "WITH", "EXPLAIN"]
        if not any(query_upper.startswith(starter) for starter in allowed_starters):
            return [{"error": "Only SELECT, WITH (CTE), and EXPLAIN queries are allowed for safety."}]

        # Block dangerous keywords
        dangerous_keywords = [
            "DROP", "DELETE", "INSERT", "UPDATE", "ALTER", "CREATE",
            "TRUNCATE", "REPLACE", "LOAD", "OUTFILE", "DUMPFILE"
        ]
        for keyword in dangerous_keywords:
            if keyword in query_upper:
                return [{"error": f"Query contains forbidden keyword: {keyword}"}]

        try:
            connection = get_mysql_connection()
            cursor = connection.cursor(dictionary=True)

            # Add LIMIT if not present (unless it's an EXPLAIN query)
            if not explain and "LIMIT" not in query_upper:
                query = f"{query.rstrip(';')} LIMIT {limit}"

            start_time = time.time()

            # Handle EXPLAIN explicitly
            if explain and not query_upper.startswith("EXPLAIN"):
                cursor.execute(f"EXPLAIN {query}")
                results = cursor.fetchall()
                logger.info(f"Query explanation retrieved: {query}")
            else:
                cursor.execute(query)
                results = cursor.fetchmany(limit)
                logger.info(
                    f"Query executed: {query}, "
                    f"Rows returned: {len(results)}, "
                    f"Time: {time.time() - start_time:.2f}s"
                )

            if not results:
                return [{"message": "No data found for your query."}]

            return results

        except Error as e:
            logger.error(f"Query execution error: {str(e)}")
            return [{"error": str(e)}]

        finally:
            if 'connection' in locals() and connection.is_connected():
                cursor.close()
                connection.close()
