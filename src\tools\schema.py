# src/tools/schema.py
import sys
import os
from utils import setup_logger
from mysql.connector import <PERSON><PERSON><PERSON>
from cachetools import TTLCache
from database.connection import get_mysql_connection

# Set up logging
logger = setup_logger(__name__)

# Cache schema for 1 hour
schema_cache = TTLCache(maxsize=10, ttl=3600)

def get_schema(mcp):
    """Register the schema tool with the MCP server."""
    
    @mcp.tool()
    def list_tables_and_schema() -> list:
        """Get a list of all tables and their columns, data types, keys, and relationships from the database."""
        db = os.getenv("MYSQL_DATABASE")
        logger.info(f"Retrieving schema for database: {db}")        
        try:
            # Check cache first
            cache_key = f"all_tables_{db}"
            if cache_key in schema_cache:
                logger.info(f"Returning cached schema for {db}")
                return schema_cache[cache_key]

            connection = get_mysql_connection()
            cursor = connection.cursor(dictionary=True)

            # Query for table and column metadata
            query = f"""
                SELECT 
                    c.TABLE_NAME, 
                    c.COLUMN_NAME, 
                    c.DATA_TYPE, 
                    c.IS_NULLABLE,
                    c.COLUMN_DEFAULT,
                    c.COLUMN_COMMENT,
                    CASE WHEN kcu.COLUMN_NAME IS NOT NULL THEN 'YES' ELSE 'NO' END AS IS_PRIMARY_KEY,
                    fk.REFERENCED_TABLE_NAME,
                    fk.REFERENCED_COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS c
                LEFT JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
                    ON c.TABLE_NAME = kcu.TABLE_NAME 
                    AND c.COLUMN_NAME = kcu.COLUMN_NAME
                    AND kcu.CONSTRAINT_SCHEMA = '{db}'
                    AND kcu.CONSTRAINT_NAME = 'PRIMARY'
                LEFT JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE fk
                    ON c.TABLE_NAME = fk.TABLE_NAME 
                    AND c.COLUMN_NAME = fk.COLUMN_NAME
                    AND fk.CONSTRAINT_SCHEMA = '{db}'
                    AND fk.REFERENCED_TABLE_NAME IS NOT NULL
                WHERE c.TABLE_SCHEMA = '{db}';
            """
            logger.info(f"Executing query: {query}")
            cursor.execute(query)
            results = cursor.fetchall()
            
            # Group results by table
            grouped_results = {}
            for row in results:
                table = row["TABLE_NAME"]
                if table not in grouped_results:
                    grouped_results[table] = {
                        "table_name": table,
                        "columns": [],
                        "primary_keys": [],
                        "foreign_keys": []
                    }
                grouped_results[table]["columns"].append({
                    "column_name": row["COLUMN_NAME"],
                    "data_type": row["DATA_TYPE"],
                    "is_nullable": row["IS_NULLABLE"],
                    "default": row["COLUMN_DEFAULT"],
                    "comment": row["COLUMN_COMMENT"]
                })
                if row["IS_PRIMARY_KEY"] == "YES":
                    grouped_results[table]["primary_keys"].append(row["COLUMN_NAME"])
                if row["REFERENCED_TABLE_NAME"]:
                    grouped_results[table]["foreign_keys"].append({
                        "column_name": row["COLUMN_NAME"],
                        "referenced_table": row["REFERENCED_TABLE_NAME"],
                        "referenced_column": row["REFERENCED_COLUMN_NAME"]
                    })

            final_results = list(grouped_results.values())
            schema_cache[cache_key] = final_results
            
            logger.info(f"Schema retrieved successfully for {db}")
            return final_results
        except Error as e:
            logger.error(f"Schema retrieval error: {str(e)}")
            return [{"error": str(e)}]
        finally:
            if 'connection' in locals() and connection.is_connected():
                cursor.close()
                connection.close()