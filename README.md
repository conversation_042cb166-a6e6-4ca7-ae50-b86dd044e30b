# MCP Server

A Model Context Protocol (MCP) server implementation that provides a stateful server for handling various tools and functionalities.

## Introduction

This project implements a Model Context Protocol server using FastMCP, designed to maintain session state and provide various tools through a streamable HTTP transport layer. The server includes a comprehensive logging system and modular tool registration capabilities.

## Prerequisites

- Python 3.13 or higher
- uv (Python virtual environment manager) 

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd MCPServer
   ```

2. Install uv if you haven't already:

   * On Mac or Linux
      ```bash
      curl -LsSf https://astral.sh/uv/install.sh | sh
      ```

   * Or on Windows using PowerShell:
      ```powershell
      powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
      ```

3. Create a virtual environment and install dependencies in one command:
   ```bash
   uv sync
   uv lock
   ```

4. Activate the virtual environment:
   - Windows (PowerShell):
     ```powershell
     .\.venv\Scripts\activate
     ```
   - Linux/macOS:
     ```bash
     source .venv/bin/activate
     ```

   Note: uv provides faster package installation and better dependency resolution than traditional pip.

## Project Structure

```
MCPServer/
├── src/
│   ├── __init__.py
│   ├── __main__.py
│   ├── server.py         # Main server implementation
│   ├── database/         # Database related modules
│   ├── resources/        # Resource files
│   ├── tools/           # MCP tools implementation
│   └── utils/           # Utility functions including logging
├── tests/               # Test files
├── logs/               # Log files directory
├── docs/               # Documentation
├── requirements.txt    # Project dependencies
└── README.md
```

## Running the Server

1. Ensure your virtual environment is activated

2. Run the server from the project root:
   ```bash
   uv run src
   ```

The server will start with the following configurations:
- Transport: streamable-http
- Logging: Enabled (both console and file)
- Log file location: `logs/mcp_server.log`

## Logging

The server uses a comprehensive logging system that:
- Logs to both console and file
- Rotates log files (max 10MB per file)
- Maintains up to 5 backup log files
- Includes timestamps, log levels, and module information

You can find the logs in:
- Console output (basic information)
- `logs/mcp_server.log` (detailed logs)

## Development

### Adding New Tools

1. Create your tool in the `src/tools` directory
2. Register your tool in the tools registry
3. Import and register in the main server

Example:
```python
from utils import setup_logger

logger = setup_logger(__name__)

def my_new_tool():
    logger.info("Executing new tool...")
    # Tool implementation
```

## Testing

Run the tests using:
```bash
python -m pytest tests/
```

## Troubleshooting

Common issues and solutions:

1. Server won't start:
   - Check if the port is available
   - Ensure all dependencies are installed
   - Check the logs for detailed error messages

2. Logging issues:
   - Verify write permissions in the logs directory
   - Check available disk space

## Contributing

...

## License

[Add your license information here]

## Support

For support and questions, please:
- Check the documentation in the `docs` directory
- Submit issues through the issue tracker
- Contact the development team