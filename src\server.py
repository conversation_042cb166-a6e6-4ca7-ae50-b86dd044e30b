from mcp.server.fastmcp import FastMCP
from mcp.server.auth.provider import AccessToken, TokenVerifier
from mcp.server.auth.settings import AuthSettings
from pydantic import AnyHttpUrl
from datetime import datetime, timedelta, timezone
from dotenv import load_dotenv
import os
from tools import register_tools
from utils import setup_logger

# Load environment variables from the .env file
load_dotenv()

# Create logger for this module
logger = setup_logger(__name__)
MCP_ENV = os.getenv("MCP_ENV", "dev")


class SimpleTokenVerifier(TokenVerifier):
    async def verify_token(self, token: str) -> AccessToken | None:
        if token == os.getenv("MCP_TOKEN"):
            return AccessToken(
                token=token,
                client_id="demo-client",
                subject="demo-user",
                scopes=["user"],
                expires_at=int((datetime.now(timezone.utc) + timedelta(hours=1)).timestamp()),
            )
        return None

def main():
    try:
        # Stateful server (maintains session state)
        logger.info("Initializing MCP Server...")
        logger.info(f"Environment: {MCP_ENV}")
        if MCP_ENV == "dev":
            logger.info("Development environment detected. Starting server...")
            western_digital_mcp = FastMCP("Weather Service", port=8000, host="0.0.0.0")
        elif MCP_ENV == "prod":
            logger.info("Production environment detected. Starting server...")
            western_digital_mcp = FastMCP(
                "Weather Service",
                token_verifier=SimpleTokenVerifier(),
                auth=AuthSettings(
                    issuer_url=AnyHttpUrl(os.getenv("AUTH_SERVER_URL")), # Authorization Server URL
                    resource_server_url=AnyHttpUrl(os.getenv("RESOURCE_SERVER_URL")), # This server's URL
                    required_scopes=["user"],
                ),
                port=8000,
                host="0.0.0.0",
            )
        else:
            logger.error(f"Unknown environment: {MCP_ENV}")
            raise ValueError(f"Unknown environment: {MCP_ENV}")
        
        logger.info("Registering tools...")
        register_tools(western_digital_mcp)
        
        logger.info("Starting server with streamable-http transport...")
        western_digital_mcp.run(transport="streamable-http",) #stdio, streamable-http
    except Exception as e:
        logger.error(f"Failed to start server: {str(e)}", exc_info=True)
        raise


